-- Create Database and Tables for CRUD Operations
-- Execute this script in phpMyAdmin to set up the database

-- Create database
CREATE DATABASE IF NOT EXISTS crud_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE crud_database;

-- Create users table for demonstration
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name VA<PERSON>HAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    date_of_birth DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create products table for demonstration
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    category VARCHAR(50),
    stock_quantity INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert sample data for users
INSERT INTO users (first_name, last_name, email, phone, address, date_of_birth) VALUES
('Ahmed', 'Hassan', '<EMAIL>', '+20123456789', 'Cairo, Egypt', '1990-05-15'),
('Fatima', 'Ali', '<EMAIL>', '+20987654321', 'Alexandria, Egypt', '1985-12-20'),
('Omar', 'Mohamed', '<EMAIL>', '+20555666777', 'Giza, Egypt', '1992-08-10');

-- Insert sample data for products
INSERT INTO products (name, description, price, category, stock_quantity) VALUES
('Laptop Dell XPS', 'High-performance laptop for professionals', 1299.99, 'Electronics', 25),
('iPhone 15', 'Latest Apple smartphone with advanced features', 999.99, 'Electronics', 50),
('Office Chair', 'Ergonomic office chair for comfortable work', 199.99, 'Furniture', 15),
('Coffee Maker', 'Automatic coffee maker with timer', 89.99, 'Appliances', 30);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_name ON products(name);
