<?php
/**
 * Test Database Connection and CRUD Operations
 * This file tests the database connection and basic CRUD operations
 */

require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Product.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار النظام - CRUD System Test</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row'>";
echo "<div class='col-12'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-vial me-2'></i>اختبار نظام CRUD</h1>";

// Test database connection
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5 class='mb-0'><i class='fas fa-database me-2'></i>اختبار الاتصال بقاعدة البيانات</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "<strong>نجح الاتصال!</strong> تم الاتصال بقاعدة البيانات بنجاح.";
        echo "</div>";
        
        // Test Users table
        echo "<h6 class='mt-3'><i class='fas fa-users me-2'></i>اختبار جدول المستخدمين:</h6>";
        $user = new User($db);
        $userCount = $user->count();
        echo "<p class='mb-1'>عدد المستخدمين: <span class='badge bg-info'>{$userCount}</span></p>";
        
        // Test Products table
        echo "<h6 class='mt-3'><i class='fas fa-box me-2'></i>اختبار جدول المنتجات:</h6>";
        $product = new Product($db);
        $productCount = $product->count();
        echo "<p class='mb-1'>عدد المنتجات: <span class='badge bg-success'>{$productCount}</span></p>";
        
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<i class='fas fa-times-circle me-2'></i>";
        echo "<strong>فشل الاتصال!</strong> لا يمكن الاتصال بقاعدة البيانات.";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "<strong>خطأ:</strong> " . $e->getMessage();
    echo "</div>";
}

echo "</div>";
echo "</div>";

// Test CRUD Operations
if (isset($db) && $db) {
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h5 class='mb-0'><i class='fas fa-cogs me-2'></i>اختبار عمليات CRUD</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    // Test User CRUD
    echo "<h6><i class='fas fa-user me-2'></i>اختبار عمليات المستخدمين:</h6>";
    
    try {
        // CREATE Test
        $testUser = new User($db);
        $testUser->first_name = "اختبار";
        $testUser->last_name = "النظام";
        $testUser->email = "<EMAIL>";
        $testUser->phone = "+20123456789";
        $testUser->address = "عنوان تجريبي";
        $testUser->date_of_birth = "1990-01-01";
        
        if ($testUser->create()) {
            echo "<p class='text-success'><i class='fas fa-check me-1'></i>CREATE: تم إنشاء مستخدم تجريبي بنجاح</p>";
            
            // READ Test
            $stmt = $testUser->read();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p class='text-success'><i class='fas fa-check me-1'></i>READ: تم قراءة " . count($users) . " مستخدم</p>";
            
            // Find the test user
            $testUserId = null;
            foreach ($users as $user) {
                if ($user['email'] == '<EMAIL>') {
                    $testUserId = $user['id'];
                    break;
                }
            }
            
            if ($testUserId) {
                // UPDATE Test
                $testUser->id = $testUserId;
                $testUser->first_name = "اختبار محدث";
                if ($testUser->update()) {
                    echo "<p class='text-success'><i class='fas fa-check me-1'></i>UPDATE: تم تحديث المستخدم التجريبي بنجاح</p>";
                }
                
                // DELETE Test
                if ($testUser->delete()) {
                    echo "<p class='text-success'><i class='fas fa-check me-1'></i>DELETE: تم حذف المستخدم التجريبي بنجاح</p>";
                }
            }
        }
    } catch (Exception $e) {
        echo "<p class='text-danger'><i class='fas fa-times me-1'></i>خطأ في اختبار المستخدمين: " . $e->getMessage() . "</p>";
    }
    
    // Test Product CRUD
    echo "<h6 class='mt-3'><i class='fas fa-box me-2'></i>اختبار عمليات المنتجات:</h6>";
    
    try {
        // CREATE Test
        $testProduct = new Product($db);
        $testProduct->name = "منتج تجريبي";
        $testProduct->description = "وصف المنتج التجريبي";
        $testProduct->price = 99.99;
        $testProduct->category = "اختبار";
        $testProduct->stock_quantity = 50;
        
        if ($testProduct->create()) {
            echo "<p class='text-success'><i class='fas fa-check me-1'></i>CREATE: تم إنشاء منتج تجريبي بنجاح</p>";
            
            // READ Test
            $stmt = $testProduct->read();
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<p class='text-success'><i class='fas fa-check me-1'></i>READ: تم قراءة " . count($products) . " منتج</p>";
            
            // Find the test product
            $testProductId = null;
            foreach ($products as $product) {
                if ($product['name'] == 'منتج تجريبي') {
                    $testProductId = $product['id'];
                    break;
                }
            }
            
            if ($testProductId) {
                // UPDATE Test
                $testProduct->id = $testProductId;
                $testProduct->name = "منتج تجريبي محدث";
                if ($testProduct->update()) {
                    echo "<p class='text-success'><i class='fas fa-check me-1'></i>UPDATE: تم تحديث المنتج التجريبي بنجاح</p>";
                }
                
                // DELETE Test
                if ($testProduct->delete()) {
                    echo "<p class='text-success'><i class='fas fa-check me-1'></i>DELETE: تم حذف المنتج التجريبي بنجاح</p>";
                }
            }
        }
    } catch (Exception $e) {
        echo "<p class='text-danger'><i class='fas fa-times me-1'></i>خطأ في اختبار المنتجات: " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
    echo "</div>";
}

// System Information
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5 class='mb-0'><i class='fas fa-info-circle me-2'></i>معلومات النظام</h5>";
echo "</div>";
echo "<div class='card-body'>";

echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6>معلومات الخادم:</h6>";
echo "<ul class='list-unstyled'>";
echo "<li><strong>PHP Version:</strong> " . phpversion() . "</li>";
echo "<li><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
echo "<li><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h6>روابط مفيدة:</h6>";
echo "<ul class='list-unstyled'>";
echo "<li><a href='index.php' class='btn btn-sm btn-primary me-2'><i class='fas fa-home me-1'></i>الصفحة الرئيسية</a></li>";
echo "<li><a href='pages/users.php' class='btn btn-sm btn-info me-2'><i class='fas fa-users me-1'></i>إدارة المستخدمين</a></li>";
echo "<li><a href='pages/products.php' class='btn btn-sm btn-success me-2'><i class='fas fa-box me-1'></i>إدارة المنتجات</a></li>";
echo "<li><a href='http://localhost/phpmyadmin' target='_blank' class='btn btn-sm btn-warning'><i class='fas fa-database me-1'></i>phpMyAdmin</a></li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";

echo "</div>";
echo "</div>";
echo "</div>";

echo "</body>";
echo "</html>";
?>
