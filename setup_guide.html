<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل تشغيل نظام CRUD</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .step-card { transition: transform 0.3s ease; }
        .step-card:hover { transform: translateY(-5px); }
        .step-number { 
            width: 40px; 
            height: 40px; 
            border-radius: 50%; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <!-- Header -->
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary">
                <i class="fas fa-rocket me-3"></i>
                دليل تشغيل نظام CRUD
            </h1>
            <p class="lead text-muted">اتبع هذه الخطوات لتشغيل التطبيق بنجاح</p>
        </div>

        <!-- Alert -->
        <div class="alert alert-warning text-center mb-5">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h4>تحتاج إلى تثبيت PHP و MySQL</h4>
            <p class="mb-0">هذا التطبيق يحتاج إلى خادم PHP وقاعدة بيانات MySQL للعمل بشكل صحيح</p>
        </div>

        <!-- Steps -->
        <div class="row">
            <!-- Step 1 -->
            <div class="col-md-6 mb-4">
                <div class="card step-card h-100">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex align-items-center">
                            <div class="step-number bg-white text-primary me-3">1</div>
                            <h5 class="mb-0">تحميل وتثبيت XAMPP</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <p><strong>XAMPP</strong> يحتوي على كل ما تحتاجه:</p>
                        <ul>
                            <li>Apache Web Server</li>
                            <li>MySQL Database</li>
                            <li>PHP</li>
                            <li>phpMyAdmin</li>
                        </ul>
                        <a href="https://www.apachefriends.org/download.html" 
                           class="btn btn-primary w-100" target="_blank">
                            <i class="fas fa-download me-2"></i>
                            تحميل XAMPP
                        </a>
                    </div>
                </div>
            </div>

            <!-- Step 2 -->
            <div class="col-md-6 mb-4">
                <div class="card step-card h-100">
                    <div class="card-header bg-success text-white">
                        <div class="d-flex align-items-center">
                            <div class="step-number bg-white text-success me-3">2</div>
                            <h5 class="mb-0">تشغيل الخدمات</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <p>بعد تثبيت XAMPP:</p>
                        <ol>
                            <li>افتح XAMPP Control Panel</li>
                            <li>اضغط <strong>"Start"</strong> بجانب Apache</li>
                            <li>اضغط <strong>"Start"</strong> بجانب MySQL</li>
                            <li>تأكد من ظهور اللون الأخضر</li>
                        </ol>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            قد تحتاج إلى تشغيل XAMPP كمدير
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3 -->
            <div class="col-md-6 mb-4">
                <div class="card step-card h-100">
                    <div class="card-header bg-warning text-white">
                        <div class="d-flex align-items-center">
                            <div class="step-number bg-white text-warning me-3">3</div>
                            <h5 class="mb-0">نسخ ملفات المشروع</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <p>انسخ مجلد CRUD إلى:</p>
                        <div class="bg-light p-3 rounded mb-3">
                            <code>C:\xampp\htdocs\CRUD</code>
                        </div>
                        <p>بحيث يصبح المسار:</p>
                        <ul>
                            <li><code>C:\xampp\htdocs\CRUD\index.php</code></li>
                            <li><code>C:\xampp\htdocs\CRUD\config\</code></li>
                            <li><code>C:\xampp\htdocs\CRUD\classes\</code></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Step 4 -->
            <div class="col-md-6 mb-4">
                <div class="card step-card h-100">
                    <div class="card-header bg-info text-white">
                        <div class="d-flex align-items-center">
                            <div class="step-number bg-white text-info me-3">4</div>
                            <h5 class="mb-0">إنشاء قاعدة البيانات</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>اذهب إلى <a href="http://localhost/phpmyadmin" target="_blank">phpMyAdmin</a></li>
                            <li>اضغط على "New" لإنشاء قاعدة بيانات جديدة</li>
                            <li>اكتب اسم قاعدة البيانات: <code>crud_database</code></li>
                            <li>افتح ملف <code>sql/create_database.sql</code></li>
                            <li>انسخ والصق المحتوى في phpMyAdmin</li>
                            <li>اضغط "Go" لتنفيذ الكود</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Final Step -->
        <div class="card mb-5">
            <div class="card-header bg-dark text-white">
                <div class="d-flex align-items-center">
                    <div class="step-number bg-white text-dark me-3">5</div>
                    <h5 class="mb-0">تشغيل التطبيق</h5>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p>بعد إكمال الخطوات السابقة، افتح المتصفح واذهب إلى:</p>
                        <div class="bg-success text-white p-3 rounded mb-3">
                            <h4 class="mb-0">
                                <i class="fas fa-globe me-2"></i>
                                <a href="http://localhost/CRUD" class="text-white text-decoration-none">
                                    http://localhost/CRUD
                                </a>
                            </h4>
                        </div>
                        <p>يجب أن ترى الصفحة الرئيسية مع رسالة نجاح الاتصال بقاعدة البيانات.</p>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            <h6 class="mt-2">جاهز للاستخدام!</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Troubleshooting -->
        <div class="card mb-5">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    حل المشاكل الشائعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-times-circle text-danger me-2"></i>خطأ في الاتصال بقاعدة البيانات</h6>
                        <ul>
                            <li>تأكد من تشغيل MySQL في XAMPP</li>
                            <li>تحقق من إنشاء قاعدة البيانات <code>crud_database</code></li>
                            <li>تأكد من تنفيذ ملف SQL</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-times-circle text-danger me-2"></i>الصفحة لا تظهر</h6>
                        <ul>
                            <li>تأكد من تشغيل Apache في XAMPP</li>
                            <li>تحقق من نسخ الملفات إلى <code>htdocs</code></li>
                            <li>جرب <code>http://localhost</code> أولاً</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Links -->
        <div class="text-center">
            <h5>روابط مفيدة:</h5>
            <a href="http://localhost" class="btn btn-outline-primary me-2" target="_blank">
                <i class="fas fa-home me-1"></i>localhost
            </a>
            <a href="http://localhost/phpmyadmin" class="btn btn-outline-info me-2" target="_blank">
                <i class="fas fa-database me-1"></i>phpMyAdmin
            </a>
            <a href="http://localhost/CRUD" class="btn btn-outline-success" target="_blank">
                <i class="fas fa-rocket me-1"></i>تطبيق CRUD
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
