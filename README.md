# نظام إدارة CRUD - CRUD Management System

نظام شامل لإدارة البيانات باستخدام عمليات الإنشاء والقراءة والتحديث والحذف (CRUD) مع قاعدة بيانات MySQL وواجهة phpMyAdmin.

## المميزات الرئيسية

### 🔧 العمليات الأساسية
- **إنشاء (Create)**: إضافة مستخدمين ومنتجات جديدة
- **قراءة (Read)**: عرض وتصفح البيانات مع إمكانية البحث والفلترة
- **تحديث (Update)**: تعديل البيانات الموجودة
- **حذف (Delete)**: حذف البيانات مع تأكيد الأمان

### 📊 إدارة المستخدمين
- إضافة وتعديل بيانات المستخدمين
- البحث في المستخدمين بالاسم أو البريد الإلكتروني
- عرض تفاصيل المستخدمين مع التواريخ
- إحصائيات عدد المستخدمين

### 🛍️ إدارة المنتجات
- إدارة كاملة للمنتجات والمخزون
- تصنيف المنتجات حسب الفئات
- تتبع المخزون وتنبيهات المخزون المنخفض
- إحصائيات شاملة للمنتجات والقيم

### 🎨 واجهة المستخدم
- تصميم عصري ومتجاوب باستخدام Bootstrap 5
- دعم اللغة العربية مع RTL
- رسائل تفاعلية وتأكيدات
- أيقونات Font Awesome

## متطلبات النظام

- **خادم ويب**: Apache/Nginx
- **PHP**: الإصدار 7.4 أو أحدث
- **قاعدة البيانات**: MySQL 5.7 أو أحدث
- **phpMyAdmin**: للإدارة المرئية لقاعدة البيانات

## التثبيت والإعداد

### 1. إعداد قاعدة البيانات

1. افتح phpMyAdmin على الرابط: `http://localhost/phpmyadmin`
2. قم بتنفيذ ملف SQL الموجود في `sql/create_database.sql`
3. سيتم إنشاء قاعدة البيانات `crud_database` مع الجداول والبيانات التجريبية

### 2. تكوين الاتصال

تحقق من إعدادات قاعدة البيانات في ملف `config/database.php`:

```php
private $host = 'localhost';
private $db_name = 'crud_database';
private $username = 'root';
private $password = '';
```

### 3. تشغيل التطبيق

1. ضع ملفات المشروع في مجلد الخادم (htdocs/www)
2. افتح المتصفح وانتقل إلى: `http://localhost/CRUD`
3. ستظهر الصفحة الرئيسية مع حالة الاتصال بقاعدة البيانات

## هيكل المشروع

```
CRUD/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── classes/
│   ├── User.php             # فئة إدارة المستخدمين
│   └── Product.php          # فئة إدارة المنتجات
├── api/
│   ├── users_api.php        # API المستخدمين
│   └── products_api.php     # API المنتجات
├── pages/
│   ├── users.php            # صفحة إدارة المستخدمين
│   └── products.php         # صفحة إدارة المنتجات
├── assets/
│   ├── css/
│   │   └── style.css        # ملفات التنسيق
│   └── js/
│       ├── users.js         # JavaScript للمستخدمين
│       └── products.js      # JavaScript للمنتجات
├── sql/
│   └── create_database.sql  # ملف إنشاء قاعدة البيانات
├── index.php               # الصفحة الرئيسية
└── README.md               # هذا الملف
```

## استخدام النظام

### إدارة المستخدمين

1. انتقل إلى "إدارة المستخدمين" من القائمة الرئيسية
2. اضغط "إضافة مستخدم جديد" لإضافة مستخدم
3. استخدم شريط البحث للبحث في المستخدمين
4. اضغط على أيقونات التعديل أو الحذف لإدارة البيانات

### إدارة المنتجات

1. انتقل إلى "إدارة المنتجات" من القائمة الرئيسية
2. شاهد الإحصائيات في الأعلى (إجمالي المنتجات، المخزون المنخفض، إلخ)
3. استخدم الفلاتر للبحث حسب الفئة أو حالة المخزون
4. أضف أو عدل المنتجات باستخدام النماذج المنبثقة

## العمليات المتاحة

### عمليات SQL المستخدمة

#### المستخدمين (Users)
- **CREATE**: `INSERT INTO users SET ...`
- **READ**: `SELECT * FROM users ORDER BY created_at DESC`
- **UPDATE**: `UPDATE users SET ... WHERE id = ?`
- **DELETE**: `DELETE FROM users WHERE id = ?`
- **SEARCH**: `SELECT * FROM users WHERE first_name LIKE ? OR last_name LIKE ? OR email LIKE ?`

#### المنتجات (Products)
- **CREATE**: `INSERT INTO products SET ...`
- **READ**: `SELECT * FROM products ORDER BY created_at DESC`
- **UPDATE**: `UPDATE products SET ... WHERE id = ?`
- **DELETE**: `DELETE FROM products WHERE id = ?`
- **SEARCH**: `SELECT * FROM products WHERE name LIKE ? OR category LIKE ?`
- **LOW STOCK**: `SELECT * FROM products WHERE stock_quantity <= ?`

## الأمان والحماية

- تنظيف البيانات المدخلة باستخدام `htmlspecialchars()` و `strip_tags()`
- استخدام Prepared Statements لمنع SQL Injection
- التحقق من صحة البيانات في الواجهة الأمامية والخلفية
- رسائل خطأ واضحة ومفيدة

## التطوير والتخصيص

### إضافة جداول جديدة

1. أنشئ فئة جديدة في مجلد `classes/`
2. أضف API endpoint في مجلد `api/`
3. أنشئ صفحة إدارة في مجلد `pages/`
4. أضف JavaScript للتفاعل في مجلد `assets/js/`

### تخصيص التصميم

- عدل ملف `assets/css/style.css` لتغيير الألوان والخطوط
- استخدم متغيرات Bootstrap لتخصيص سريع
- أضف أيقونات Font Awesome جديدة حسب الحاجة

## استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من تشغيل خادم MySQL
   - تحقق من إعدادات الاتصال في `config/database.php`

2. **لا تظهر البيانات**
   - تأكد من تنفيذ ملف `sql/create_database.sql`
   - تحقق من وجود البيانات في phpMyAdmin

3. **أخطاء JavaScript**
   - افتح أدوات المطور في المتصفح
   - تحقق من وحدة التحكم للأخطاء

## الدعم والمساهمة

هذا المشروع مفتوح المصدر ويمكن تطويره وتحسينه. لأي استفسارات أو اقتراحات، يرجى فتح issue أو pull request.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.
