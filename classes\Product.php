<?php
/**
 * Product Class for CRUD Operations
 * Handles all database operations for products table
 */

// Database config will be included by the calling file

class Product {
    private $conn;
    private $table_name = "products";
    
    // Product properties
    public $id;
    public $name;
    public $description;
    public $price;
    public $category;
    public $stock_quantity;
    public $created_at;
    public $updated_at;
    
    public function __construct($db) {
        $this->conn = $db;
    }
    
    /**
     * CREATE - Insert new product
     * @return bool
     */
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET name=:name, 
                      description=:description, 
                      price=:price, 
                      category=:category, 
                      stock_quantity=:stock_quantity";
        
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->price = htmlspecialchars(strip_tags($this->price));
        $this->category = htmlspecialchars(strip_tags($this->category));
        $this->stock_quantity = htmlspecialchars(strip_tags($this->stock_quantity));
        
        // Bind data
        $stmt->bindParam(":name", $this->name);
        $stmt->bindParam(":description", $this->description);
        $stmt->bindParam(":price", $this->price);
        $stmt->bindParam(":category", $this->category);
        $stmt->bindParam(":stock_quantity", $this->stock_quantity);
        
        if($stmt->execute()) {
            return true;
        }
        return false;
    }
    
    /**
     * READ - Get all products
     * @return PDOStatement
     */
    public function read() {
        $query = "SELECT id, name, description, price, category, stock_quantity, created_at, updated_at 
                  FROM " . $this->table_name . " 
                  ORDER BY created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }
    
    /**
     * READ ONE - Get single product by ID
     * @return bool
     */
    public function readOne() {
        $query = "SELECT id, name, description, price, category, stock_quantity, created_at, updated_at 
                  FROM " . $this->table_name . " 
                  WHERE id = ? 
                  LIMIT 0,1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if($row) {
            $this->name = $row['name'];
            $this->description = $row['description'];
            $this->price = $row['price'];
            $this->category = $row['category'];
            $this->stock_quantity = $row['stock_quantity'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }
        return false;
    }
    
    /**
     * UPDATE - Update product data
     * @return bool
     */
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET name = :name, 
                      description = :description, 
                      price = :price, 
                      category = :category, 
                      stock_quantity = :stock_quantity 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        // Clean data
        $this->name = htmlspecialchars(strip_tags($this->name));
        $this->description = htmlspecialchars(strip_tags($this->description));
        $this->price = htmlspecialchars(strip_tags($this->price));
        $this->category = htmlspecialchars(strip_tags($this->category));
        $this->stock_quantity = htmlspecialchars(strip_tags($this->stock_quantity));
        $this->id = htmlspecialchars(strip_tags($this->id));
        
        // Bind data
        $stmt->bindParam(':name', $this->name);
        $stmt->bindParam(':description', $this->description);
        $stmt->bindParam(':price', $this->price);
        $stmt->bindParam(':category', $this->category);
        $stmt->bindParam(':stock_quantity', $this->stock_quantity);
        $stmt->bindParam(':id', $this->id);
        
        if($stmt->execute()) {
            return true;
        }
        return false;
    }
    
    /**
     * DELETE - Delete product
     * @return bool
     */
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        
        $this->id = htmlspecialchars(strip_tags($this->id));
        
        $stmt->bindParam(1, $this->id);
        
        if($stmt->execute()) {
            return true;
        }
        return false;
    }
    
    /**
     * SEARCH - Search products by name or category
     * @param string $keywords
     * @return PDOStatement
     */
    public function search($keywords) {
        $query = "SELECT id, name, description, price, category, stock_quantity, created_at, updated_at 
                  FROM " . $this->table_name . " 
                  WHERE name LIKE ? OR category LIKE ? OR description LIKE ? 
                  ORDER BY created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        
        $keywords = htmlspecialchars(strip_tags($keywords));
        $keywords = "%{$keywords}%";
        
        $stmt->bindParam(1, $keywords);
        $stmt->bindParam(2, $keywords);
        $stmt->bindParam(3, $keywords);
        
        $stmt->execute();
        
        return $stmt;
    }
    
    /**
     * COUNT - Get total number of products
     * @return int
     */
    public function count() {
        $query = "SELECT COUNT(*) as total_rows FROM " . $this->table_name;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $row['total_rows'];
    }
    
    /**
     * GET CATEGORIES - Get all unique categories
     * @return PDOStatement
     */
    public function getCategories() {
        $query = "SELECT DISTINCT category FROM " . $this->table_name . " WHERE category IS NOT NULL ORDER BY category";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }
    
    /**
     * GET LOW STOCK - Get products with low stock
     * @param int $threshold
     * @return PDOStatement
     */
    public function getLowStock($threshold = 10) {
        $query = "SELECT id, name, description, price, category, stock_quantity, created_at, updated_at 
                  FROM " . $this->table_name . " 
                  WHERE stock_quantity <= ? 
                  ORDER BY stock_quantity ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $threshold);
        $stmt->execute();
        
        return $stmt;
    }
}
?>
