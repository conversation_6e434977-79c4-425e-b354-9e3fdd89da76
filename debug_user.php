<?php
/**
 * Debug User Creation
 * ملف لتشخيص مشاكل إضافة المستخدمين
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تشخيص مشكلة إضافة المستخدم</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<h1 class='text-center mb-4'>🔍 تشخيص مشكلة إضافة المستخدم</h1>";

// Test 1: Database Connection
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5 class='mb-0'>1. اختبار الاتصال بقاعدة البيانات</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<div class='alert alert-success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
        
        // Test 2: Check if users table exists
        echo "<h6>2. فحص جدول المستخدمين:</h6>";
        $stmt = $db->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='text-success'>✅ جدول المستخدمين موجود</p>";
            
            // Test 3: Check table structure
            echo "<h6>3. هيكل جدول المستخدمين:</h6>";
            $stmt = $db->query("DESCRIBE users");
            echo "<table class='table table-sm'>";
            echo "<thead><tr><th>الحقل</th><th>النوع</th><th>Null</th><th>Key</th></tr></thead>";
            echo "<tbody>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr>";
                echo "<td>{$row['Field']}</td>";
                echo "<td>{$row['Type']}</td>";
                echo "<td>{$row['Null']}</td>";
                echo "<td>{$row['Key']}</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
        } else {
            echo "<p class='text-danger'>❌ جدول المستخدمين غير موجود</p>";
            echo "<div class='alert alert-warning'>";
            echo "يجب تنفيذ ملف sql/create_database.sql في phpMyAdmin";
            echo "</div>";
        }
    } else {
        echo "<div class='alert alert-danger'>❌ فشل الاتصال بقاعدة البيانات</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ خطأ: " . $e->getMessage() . "</div>";
}

echo "</div></div>";

// Test 4: Test User Class
if (isset($db) && $db) {
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h5 class='mb-0'>4. اختبار فئة المستخدم</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    try {
        require_once 'classes/User.php';
        $user = new User($db);
        echo "<p class='text-success'>✅ تم إنشاء كائن المستخدم بنجاح</p>";
        
        // Test create method
        echo "<h6>5. اختبار إضافة مستخدم تجريبي:</h6>";
        
        $user->first_name = "اختبار";
        $user->last_name = "النظام";
        $user->email = "test_" . time() . "@example.com"; // Unique email
        $user->phone = "+20123456789";
        $user->address = "عنوان تجريبي";
        $user->date_of_birth = "1990-01-01";
        
        if ($user->create()) {
            echo "<div class='alert alert-success'>✅ تم إضافة المستخدم التجريبي بنجاح!</div>";
            
            // Get the last inserted user
            $stmt = $db->query("SELECT * FROM users ORDER BY id DESC LIMIT 1");
            $lastUser = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($lastUser) {
                echo "<h6>بيانات آخر مستخدم مضاف:</h6>";
                echo "<table class='table table-sm'>";
                foreach ($lastUser as $key => $value) {
                    echo "<tr><td><strong>{$key}</strong></td><td>{$value}</td></tr>";
                }
                echo "</table>";
                
                // Clean up - delete test user
                $deleteStmt = $db->prepare("DELETE FROM users WHERE id = ?");
                $deleteStmt->execute([$lastUser['id']]);
                echo "<p class='text-info'>🧹 تم حذف المستخدم التجريبي</p>";
            }
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في إضافة المستخدم التجريبي</div>";
            
            // Get last error
            $errorInfo = $db->errorInfo();
            if ($errorInfo[0] !== '00000') {
                echo "<p class='text-danger'>تفاصيل الخطأ: " . $errorInfo[2] . "</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>❌ خطأ في فئة المستخدم: " . $e->getMessage() . "</div>";
    }
    
    echo "</div></div>";
}

// Test 5: Test API
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5 class='mb-0'>6. اختبار API</h5>";
echo "</div>";
echo "<div class='card-body'>";

echo "<h6>اختبار إضافة مستخدم عبر API:</h6>";
echo "<form id='testForm' class='row g-3'>";
echo "<div class='col-md-6'>";
echo "<label class='form-label'>الاسم الأول:</label>";
echo "<input type='text' class='form-control' id='firstName' value='أحمد' required>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<label class='form-label'>الاسم الأخير:</label>";
echo "<input type='text' class='form-control' id='lastName' value='محمد' required>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<label class='form-label'>البريد الإلكتروني:</label>";
echo "<input type='email' class='form-control' id='email' value='<EMAIL>' required>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<label class='form-label'>الهاتف:</label>";
echo "<input type='tel' class='form-control' id='phone' value='+20123456789'>";
echo "</div>";
echo "<div class='col-12'>";
echo "<button type='button' class='btn btn-primary' onclick='testAPI()'>اختبار إضافة المستخدم</button>";
echo "</div>";
echo "</form>";

echo "<div id='apiResult' class='mt-3'></div>";

echo "</div></div>";

// Instructions
echo "<div class='card'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h5 class='mb-0'>📋 التعليمات</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<ol>";
echo "<li><strong>إذا كان الاتصال بقاعدة البيانات فاشل:</strong> تأكد من تشغيل MySQL في XAMPP</li>";
echo "<li><strong>إذا كان جدول المستخدمين غير موجود:</strong> نفذ ملف sql/create_database.sql في phpMyAdmin</li>";
echo "<li><strong>إذا فشل اختبار فئة المستخدم:</strong> تحقق من أذونات قاعدة البيانات</li>";
echo "<li><strong>إذا فشل اختبار API:</strong> تحقق من مسار الملفات والأذونات</li>";
echo "</ol>";
echo "</div></div>";

echo "</div>";

// JavaScript for API testing
echo "<script>";
echo "function testAPI() {";
echo "    const data = {";
echo "        first_name: document.getElementById('firstName').value,";
echo "        last_name: document.getElementById('lastName').value,";
echo "        email: document.getElementById('email').value + '_' + Date.now(),"; // Make email unique
echo "        phone: document.getElementById('phone').value,";
echo "        address: 'عنوان تجريبي',";
echo "        date_of_birth: '1990-01-01'";
echo "    };";
echo "    ";
echo "    fetch('api/users_api.php', {";
echo "        method: 'POST',";
echo "        headers: { 'Content-Type': 'application/json' },";
echo "        body: JSON.stringify(data)";
echo "    })";
echo "    .then(response => response.json())";
echo "    .then(result => {";
echo "        const resultDiv = document.getElementById('apiResult');";
echo "        if (result.success) {";
echo "            resultDiv.innerHTML = '<div class=\"alert alert-success\">✅ ' + result.message + '</div>';";
echo "        } else {";
echo "            resultDiv.innerHTML = '<div class=\"alert alert-danger\">❌ ' + result.message + '</div>';";
echo "        }";
echo "    })";
echo "    .catch(error => {";
echo "        document.getElementById('apiResult').innerHTML = '<div class=\"alert alert-danger\">❌ خطأ في الشبكة: ' + error + '</div>';";
echo "    });";
echo "}";
echo "</script>";

echo "</body></html>";
?>
