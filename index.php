<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة CRUD</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-database me-2"></i>
                نظام إدارة CRUD
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="pages/users.php">
                            <i class="fas fa-users me-1"></i>
                            إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/products.php">
                            <i class="fas fa-box me-1"></i>
                            إدارة المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="http://localhost/phpmyadmin" target="_blank">
                            <i class="fas fa-database me-1"></i>
                            phpMyAdmin
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <div class="jumbotron bg-light p-5 rounded">
                    <h1 class="display-4 text-center">مرحباً بك في نظام إدارة CRUD</h1>
                    <p class="lead text-center">نظام شامل لإدارة البيانات باستخدام عمليات الإنشاء والقراءة والتحديث والحذف</p>
                    <hr class="my-4">
                    <div class="row text-center">
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <i class="fas fa-users fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title">إدارة المستخدمين</h5>
                                    <p class="card-text">إضافة وتعديل وحذف بيانات المستخدمين</p>
                                    <a href="pages/users.php" class="btn btn-primary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        الذهاب إلى المستخدمين
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <i class="fas fa-box fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">إدارة المنتجات</h5>
                                    <p class="card-text">إضافة وتعديل وحذف بيانات المنتجات</p>
                                    <a href="pages/products.php" class="btn btn-success">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        الذهاب إلى المنتجات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Connection Status -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-server me-2"></i>
                            حالة الاتصال بقاعدة البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        require_once 'config/database.php';
                        $database = new Database();
                        
                        if ($database->testConnection()) {
                            echo '<div class="alert alert-success" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    تم الاتصال بقاعدة البيانات بنجاح!
                                  </div>';
                        } else {
                            echo '<div class="alert alert-danger" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    فشل في الاتصال بقاعدة البيانات. تأكد من تشغيل خادم MySQL.
                                  </div>';
                        }
                        ?>
                        <p class="mb-0">
                            <strong>رابط phpMyAdmin:</strong> 
                            <a href="http://localhost/phpmyadmin" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-external-link-alt me-1"></i>
                                فتح phpMyAdmin
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
