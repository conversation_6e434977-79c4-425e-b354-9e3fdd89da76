/**
 * Users Management JavaScript
 * Handles all user interface interactions and AJAX calls
 */

document.addEventListener('DOMContentLoaded', function() {
    // Load users on page load
    loadUsers();
    loadUserCount();
    
    // Add user form submission
    document.getElementById('addUserForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addUser();
    });
    
    // Edit user form submission
    document.getElementById('editUserForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateUser();
    });
    
    // Search functionality
    document.getElementById('searchBtn').addEventListener('click', function() {
        searchUsers();
    });
    
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchUsers();
        }
    });
    
    // Clear search when input is empty
    document.getElementById('searchInput').addEventListener('input', function() {
        if (this.value === '') {
            loadUsers();
        }
    });
});

/**
 * Load all users
 */
function loadUsers() {
    fetch('../api/users_api.php?action=read')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUsers(data.data);
                updateUserCount(data.count);
            } else {
                showAlert('خطأ في تحميل البيانات', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        });
}

/**
 * Display users in table
 */
function displayUsers(users) {
    const tbody = document.getElementById('usersTableBody');
    tbody.innerHTML = '';
    
    if (users.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد بيانات مستخدمين</p>
                </td>
            </tr>
        `;
        return;
    }
    
    users.forEach(user => {
        const row = `
            <tr>
                <td>${user.id}</td>
                <td>${user.first_name}</td>
                <td>${user.last_name}</td>
                <td>${user.email}</td>
                <td>${user.phone || '-'}</td>
                <td>${formatDate(user.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-info me-1" onclick="viewUser(${user.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning me-1" onclick="editUser(${user.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

/**
 * Add new user
 */
function addUser() {
    const formData = new FormData(document.getElementById('addUserForm'));
    const userData = Object.fromEntries(formData);

    // Client-side validation
    if (!userData.first_name || !userData.last_name || !userData.email) {
        showAlert('الاسم الأول والاسم الأخير والبريد الإلكتروني مطلوبة', 'danger');
        return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
        showAlert('صيغة البريد الإلكتروني غير صحيحة', 'danger');
        return;
    }

    // Show loading
    const submitBtn = document.querySelector('#addUserForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<span class="loading"></span> جاري الحفظ...';
    submitBtn.disabled = true;

    fetch('../api/users_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            document.getElementById('addUserForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            loadUsers();
            loadUserCount();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال بالخادم: ' + error.message, 'danger');
    })
    .finally(() => {
        // Restore button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

/**
 * Edit user - load user data into edit modal
 */
function editUser(userId) {
    fetch(`../api/users_api.php?action=read_one&id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const user = data.data;
                document.getElementById('editUserId').value = user.id;
                document.getElementById('editFirstName').value = user.first_name;
                document.getElementById('editLastName').value = user.last_name;
                document.getElementById('editEmail').value = user.email;
                document.getElementById('editPhone').value = user.phone || '';
                document.getElementById('editAddress').value = user.address || '';
                document.getElementById('editDateOfBirth').value = user.date_of_birth || '';
                
                new bootstrap.Modal(document.getElementById('editUserModal')).show();
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في تحميل بيانات المستخدم', 'danger');
        });
}

/**
 * Update user
 */
function updateUser() {
    const formData = new FormData(document.getElementById('editUserForm'));
    const userData = Object.fromEntries(formData);
    
    fetch('../api/users_api.php', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            loadUsers();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في تحديث المستخدم', 'danger');
    });
}

/**
 * Delete user
 */
function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        fetch(`../api/users_api.php?id=${userId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                loadUsers();
                loadUserCount();
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في حذف المستخدم', 'danger');
        });
    }
}

/**
 * Search users
 */
function searchUsers() {
    const keywords = document.getElementById('searchInput').value.trim();
    
    if (keywords === '') {
        loadUsers();
        return;
    }
    
    fetch(`../api/users_api.php?action=search&keywords=${encodeURIComponent(keywords)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUsers(data.data);
                updateUserCount(data.count);
            } else {
                showAlert('خطأ في البحث', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في البحث', 'danger');
        });
}

/**
 * Load user count
 */
function loadUserCount() {
    fetch('../api/users_api.php?action=count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateUserCount(data.count);
            }
        })
        .catch(error => {
            console.error('Error loading count:', error);
        });
}

/**
 * Update user count display
 */
function updateUserCount(count) {
    document.getElementById('totalUsers').textContent = count;
}

/**
 * View user details (placeholder for future implementation)
 */
function viewUser(userId) {
    // This can be implemented to show user details in a modal
    alert(`عرض تفاصيل المستخدم رقم: ${userId}`);
}

/**
 * Show alert message
 */
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show message`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}
