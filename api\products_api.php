<?php
/**
 * Products API - Handle CRUD operations for products
 * This file handles all AJAX requests for product management
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../classes/Product.php';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Initialize product object
$product = new Product($db);

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get action from query parameter
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch($method) {
        case 'GET':
            if($action == 'read') {
                // Read all products
                $stmt = $product->read();
                $products = array();
                
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $products[] = $row;
                }
                
                echo json_encode(array(
                    'success' => true,
                    'data' => $products,
                    'count' => count($products)
                ));
                
            } elseif($action == 'read_one' && isset($_GET['id'])) {
                // Read single product
                $product->id = $_GET['id'];
                
                if($product->readOne()) {
                    echo json_encode(array(
                        'success' => true,
                        'data' => array(
                            'id' => $product->id,
                            'name' => $product->name,
                            'description' => $product->description,
                            'price' => $product->price,
                            'category' => $product->category,
                            'stock_quantity' => $product->stock_quantity,
                            'created_at' => $product->created_at,
                            'updated_at' => $product->updated_at
                        )
                    ));
                } else {
                    echo json_encode(array(
                        'success' => false,
                        'message' => 'المنتج غير موجود'
                    ));
                }
                
            } elseif($action == 'search' && isset($_GET['keywords'])) {
                // Search products
                $stmt = $product->search($_GET['keywords']);
                $products = array();
                
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $products[] = $row;
                }
                
                echo json_encode(array(
                    'success' => true,
                    'data' => $products,
                    'count' => count($products)
                ));
                
            } elseif($action == 'count') {
                // Get product count
                $count = $product->count();
                echo json_encode(array(
                    'success' => true,
                    'count' => $count
                ));
                
            } elseif($action == 'categories') {
                // Get all categories
                $stmt = $product->getCategories();
                $categories = array();
                
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $categories[] = $row['category'];
                }
                
                echo json_encode(array(
                    'success' => true,
                    'data' => $categories
                ));
                
            } elseif($action == 'low_stock') {
                // Get low stock products
                $threshold = isset($_GET['threshold']) ? $_GET['threshold'] : 10;
                $stmt = $product->getLowStock($threshold);
                $products = array();
                
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $products[] = $row;
                }
                
                echo json_encode(array(
                    'success' => true,
                    'data' => $products,
                    'count' => count($products)
                ));
                
            } elseif($action == 'statistics') {
                // Get product statistics
                $totalProducts = $product->count();
                $lowStockStmt = $product->getLowStock(10);
                $lowStockCount = $lowStockStmt->rowCount();
                
                $categoriesStmt = $product->getCategories();
                $categoriesCount = $categoriesStmt->rowCount();
                
                // Calculate total value
                $allProductsStmt = $product->read();
                $totalValue = 0;
                while ($row = $allProductsStmt->fetch(PDO::FETCH_ASSOC)) {
                    $totalValue += $row['price'] * $row['stock_quantity'];
                }
                
                echo json_encode(array(
                    'success' => true,
                    'data' => array(
                        'total_products' => $totalProducts,
                        'low_stock_count' => $lowStockCount,
                        'categories_count' => $categoriesCount,
                        'total_value' => $totalValue
                    )
                ));
            }
            break;
            
        case 'POST':
            // Create new product
            $data = json_decode(file_get_contents("php://input"), true);
            
            if(!$data) {
                $data = $_POST;
            }
            
            $product->name = $data['name'];
            $product->description = $data['description'] ?? '';
            $product->price = $data['price'];
            $product->category = $data['category'];
            $product->stock_quantity = $data['stock_quantity'];
            
            if($product->create()) {
                echo json_encode(array(
                    'success' => true,
                    'message' => 'تم إضافة المنتج بنجاح'
                ));
            } else {
                echo json_encode(array(
                    'success' => false,
                    'message' => 'فشل في إضافة المنتج'
                ));
            }
            break;
            
        case 'PUT':
            // Update product
            $data = json_decode(file_get_contents("php://input"), true);
            
            $product->id = $data['id'];
            $product->name = $data['name'];
            $product->description = $data['description'] ?? '';
            $product->price = $data['price'];
            $product->category = $data['category'];
            $product->stock_quantity = $data['stock_quantity'];
            
            if($product->update()) {
                echo json_encode(array(
                    'success' => true,
                    'message' => 'تم تحديث بيانات المنتج بنجاح'
                ));
            } else {
                echo json_encode(array(
                    'success' => false,
                    'message' => 'فشل في تحديث بيانات المنتج'
                ));
            }
            break;
            
        case 'DELETE':
            // Delete product
            if(isset($_GET['id'])) {
                $product->id = $_GET['id'];
                
                if($product->delete()) {
                    echo json_encode(array(
                        'success' => true,
                        'message' => 'تم حذف المنتج بنجاح'
                    ));
                } else {
                    echo json_encode(array(
                        'success' => false,
                        'message' => 'فشل في حذف المنتج'
                    ));
                }
            } else {
                echo json_encode(array(
                    'success' => false,
                    'message' => 'معرف المنتج مطلوب'
                ));
            }
            break;
            
        default:
            echo json_encode(array(
                'success' => false,
                'message' => 'طريقة الطلب غير مدعومة'
            ));
            break;
    }
    
} catch(Exception $e) {
    echo json_encode(array(
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ));
}
?>
