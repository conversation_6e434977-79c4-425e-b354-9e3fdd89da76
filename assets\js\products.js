/**
 * Products Management JavaScript
 * Handles all product interface interactions and AJAX calls
 */

document.addEventListener('DOMContentLoaded', function() {
    // Load products and statistics on page load
    loadProducts();
    loadStatistics();
    loadCategories();
    
    // Add product form submission
    document.getElementById('addProductForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addProduct();
    });
    
    // Edit product form submission
    document.getElementById('editProductForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateProduct();
    });
    
    // Search functionality
    document.getElementById('searchBtn').addEventListener('click', function() {
        searchProducts();
    });
    
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchProducts();
        }
    });
    
    // Filter functionality
    document.getElementById('categoryFilter').addEventListener('change', function() {
        filterProducts();
    });
    
    document.getElementById('stockFilter').addEventListener('change', function() {
        filterProducts();
    });
    
    // Clear search when input is empty
    document.getElementById('searchInput').addEventListener('input', function() {
        if (this.value === '') {
            loadProducts();
        }
    });
});

/**
 * Load all products
 */
function loadProducts() {
    fetch('../api/products_api.php?action=read')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProducts(data.data);
            } else {
                showAlert('خطأ في تحميل البيانات', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في الاتصال بالخادم', 'danger');
        });
}

/**
 * Load statistics
 */
function loadStatistics() {
    fetch('../api/products_api.php?action=statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stats = data.data;
                document.getElementById('totalProducts').textContent = stats.total_products;
                document.getElementById('lowStockProducts').textContent = stats.low_stock_count;
                document.getElementById('totalCategories').textContent = stats.categories_count;
                document.getElementById('totalValue').textContent = '$' + parseFloat(stats.total_value).toFixed(2);
            }
        })
        .catch(error => {
            console.error('Error loading statistics:', error);
        });
}

/**
 * Load categories for filter
 */
function loadCategories() {
    fetch('../api/products_api.php?action=categories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const categoryFilter = document.getElementById('categoryFilter');
                categoryFilter.innerHTML = '<option value="">جميع الفئات</option>';
                
                data.data.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    categoryFilter.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading categories:', error);
        });
}

/**
 * Display products in table
 */
function displayProducts(products) {
    const tbody = document.getElementById('productsTableBody');
    tbody.innerHTML = '';
    
    if (products.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <i class="fas fa-box fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد منتجات</p>
                </td>
            </tr>
        `;
        return;
    }
    
    products.forEach(product => {
        const stockStatus = getStockStatus(product.stock_quantity);
        const row = `
            <tr>
                <td>${product.id}</td>
                <td>${product.name}</td>
                <td><span class="badge bg-secondary">${product.category}</span></td>
                <td>$${parseFloat(product.price).toFixed(2)}</td>
                <td>${product.stock_quantity}</td>
                <td>${stockStatus}</td>
                <td>${formatDate(product.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-info me-1" onclick="viewProduct(${product.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning me-1" onclick="editProduct(${product.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProduct(${product.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

/**
 * Get stock status badge
 */
function getStockStatus(quantity) {
    if (quantity === 0) {
        return '<span class="badge bg-danger">نفد المخزون</span>';
    } else if (quantity <= 10) {
        return '<span class="badge bg-warning">مخزون منخفض</span>';
    } else {
        return '<span class="badge bg-success">متوفر</span>';
    }
}

/**
 * Add new product
 */
function addProduct() {
    const formData = new FormData(document.getElementById('addProductForm'));
    const productData = Object.fromEntries(formData);
    
    fetch('../api/products_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            document.getElementById('addProductForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('addProductModal')).hide();
            loadProducts();
            loadStatistics();
            loadCategories();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في إضافة المنتج', 'danger');
    });
}

/**
 * Edit product - load product data into edit modal
 */
function editProduct(productId) {
    fetch(`../api/products_api.php?action=read_one&id=${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const product = data.data;
                document.getElementById('editProductId').value = product.id;
                document.getElementById('editProductName').value = product.name;
                document.getElementById('editProductCategory').value = product.category;
                document.getElementById('editProductPrice').value = product.price;
                document.getElementById('editProductStock').value = product.stock_quantity;
                document.getElementById('editProductDescription').value = product.description || '';
                
                new bootstrap.Modal(document.getElementById('editProductModal')).show();
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في تحميل بيانات المنتج', 'danger');
        });
}

/**
 * Update product
 */
function updateProduct() {
    const formData = new FormData(document.getElementById('editProductForm'));
    const productData = Object.fromEntries(formData);
    
    fetch('../api/products_api.php', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('editProductModal')).hide();
            loadProducts();
            loadStatistics();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في تحديث المنتج', 'danger');
    });
}

/**
 * Delete product
 */
function deleteProduct(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        fetch(`../api/products_api.php?id=${productId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                loadProducts();
                loadStatistics();
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في حذف المنتج', 'danger');
        });
    }
}

/**
 * Search products
 */
function searchProducts() {
    const keywords = document.getElementById('searchInput').value.trim();
    
    if (keywords === '') {
        loadProducts();
        return;
    }
    
    fetch(`../api/products_api.php?action=search&keywords=${encodeURIComponent(keywords)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProducts(data.data);
            } else {
                showAlert('خطأ في البحث', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في البحث', 'danger');
        });
}

/**
 * Filter products
 */
function filterProducts() {
    const category = document.getElementById('categoryFilter').value;
    const stockFilter = document.getElementById('stockFilter').value;
    
    if (stockFilter === 'low') {
        // Load low stock products
        fetch('../api/products_api.php?action=low_stock')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let filteredProducts = data.data;
                    if (category) {
                        filteredProducts = filteredProducts.filter(product => product.category === category);
                    }
                    displayProducts(filteredProducts);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
    } else if (stockFilter === 'out') {
        // Load out of stock products
        loadProducts();
        // This will be filtered on the client side for simplicity
        setTimeout(() => {
            const rows = document.querySelectorAll('#productsTableBody tr');
            rows.forEach(row => {
                const stockCell = row.cells[4];
                if (stockCell && parseInt(stockCell.textContent) !== 0) {
                    row.style.display = 'none';
                }
            });
        }, 100);
    } else {
        loadProducts();
    }
}

/**
 * Clear all filters
 */
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('stockFilter').value = '';
    loadProducts();
}

/**
 * View product details (placeholder for future implementation)
 */
function viewProduct(productId) {
    // This can be implemented to show product details in a modal
    alert(`عرض تفاصيل المنتج رقم: ${productId}`);
}

/**
 * Show alert message
 */
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show message`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}
