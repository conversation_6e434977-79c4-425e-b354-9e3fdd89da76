<?php
/**
 * Users API - Handle CRUD operations for users
 * This file handles all AJAX requests for user management
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../classes/User.php';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Initialize user object
$user = new User($db);

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get action from query parameter
$action = isset($_GET['action']) ? $_GET['action'] : '';

try {
    switch($method) {
        case 'GET':
            if($action == 'read') {
                // Read all users
                $stmt = $user->read();
                $users = array();
                
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $users[] = $row;
                }
                
                echo json_encode(array(
                    'success' => true,
                    'data' => $users,
                    'count' => count($users)
                ));
                
            } elseif($action == 'read_one' && isset($_GET['id'])) {
                // Read single user
                $user->id = $_GET['id'];
                
                if($user->readOne()) {
                    echo json_encode(array(
                        'success' => true,
                        'data' => array(
                            'id' => $user->id,
                            'first_name' => $user->first_name,
                            'last_name' => $user->last_name,
                            'email' => $user->email,
                            'phone' => $user->phone,
                            'address' => $user->address,
                            'date_of_birth' => $user->date_of_birth,
                            'created_at' => $user->created_at,
                            'updated_at' => $user->updated_at
                        )
                    ));
                } else {
                    echo json_encode(array(
                        'success' => false,
                        'message' => 'المستخدم غير موجود'
                    ));
                }
                
            } elseif($action == 'search' && isset($_GET['keywords'])) {
                // Search users
                $stmt = $user->search($_GET['keywords']);
                $users = array();
                
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $users[] = $row;
                }
                
                echo json_encode(array(
                    'success' => true,
                    'data' => $users,
                    'count' => count($users)
                ));
                
            } elseif($action == 'count') {
                // Get user count
                $count = $user->count();
                echo json_encode(array(
                    'success' => true,
                    'count' => $count
                ));
            }
            break;
            
        case 'POST':
            // Create new user
            $data = json_decode(file_get_contents("php://input"), true);
            
            if(!$data) {
                $data = $_POST;
            }
            
            $user->first_name = $data['first_name'];
            $user->last_name = $data['last_name'];
            $user->email = $data['email'];
            $user->phone = $data['phone'] ?? '';
            $user->address = $data['address'] ?? '';
            $user->date_of_birth = $data['date_of_birth'] ?? null;
            
            if($user->create()) {
                echo json_encode(array(
                    'success' => true,
                    'message' => 'تم إضافة المستخدم بنجاح'
                ));
            } else {
                echo json_encode(array(
                    'success' => false,
                    'message' => 'فشل في إضافة المستخدم'
                ));
            }
            break;
            
        case 'PUT':
            // Update user
            $data = json_decode(file_get_contents("php://input"), true);
            
            $user->id = $data['id'];
            $user->first_name = $data['first_name'];
            $user->last_name = $data['last_name'];
            $user->email = $data['email'];
            $user->phone = $data['phone'] ?? '';
            $user->address = $data['address'] ?? '';
            $user->date_of_birth = $data['date_of_birth'] ?? null;
            
            if($user->update()) {
                echo json_encode(array(
                    'success' => true,
                    'message' => 'تم تحديث بيانات المستخدم بنجاح'
                ));
            } else {
                echo json_encode(array(
                    'success' => false,
                    'message' => 'فشل في تحديث بيانات المستخدم'
                ));
            }
            break;
            
        case 'DELETE':
            // Delete user
            if(isset($_GET['id'])) {
                $user->id = $_GET['id'];
                
                if($user->delete()) {
                    echo json_encode(array(
                        'success' => true,
                        'message' => 'تم حذف المستخدم بنجاح'
                    ));
                } else {
                    echo json_encode(array(
                        'success' => false,
                        'message' => 'فشل في حذف المستخدم'
                    ));
                }
            } else {
                echo json_encode(array(
                    'success' => false,
                    'message' => 'معرف المستخدم مطلوب'
                ));
            }
            break;
            
        default:
            echo json_encode(array(
                'success' => false,
                'message' => 'طريقة الطلب غير مدعومة'
            ));
            break;
    }
    
} catch(Exception $e) {
    echo json_encode(array(
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ));
}
?>
